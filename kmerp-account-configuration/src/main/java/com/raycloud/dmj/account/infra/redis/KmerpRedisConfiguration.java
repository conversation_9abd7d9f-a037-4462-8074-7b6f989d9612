package com.raycloud.dmj.account.infra.redis;

import com.raycloud.dmj.kmbi.connector.adapter.RedisConnectors;
import com.raycloud.dmj.kmbi.connector.api.req.GetConnectorConfigParameter;
import com.raycloud.dmj.kmbi.connector.redis.RedissonConnector;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@RequiredArgsConstructor
@Configuration
@EnableConfigurationProperties(KmerpRedisProperties.class)
public class KmerpRedisConfiguration {

    private final RedisConnectors redisConnectors;

    public RedissonConnector getRedisConnector(String connectorId) {
        GetConnectorConfigParameter parameter = new GetConnectorConfigParameter();
        parameter.setConnectorId(connectorId);
        parameter.setTenantUid(null);
        parameter.setTenantDatabaseConnectorId(null);
        return redisConnectors.getCacheConnector(parameter);
    }

    public RedissonClient getRedissonClient(String connectorId) {
        RedissonConnector redissonConnector = getRedisConnector(connectorId);
        if (redissonConnector == null) {
            return null;
        }
        return redissonConnector.getRedissonClient();
    }

}
