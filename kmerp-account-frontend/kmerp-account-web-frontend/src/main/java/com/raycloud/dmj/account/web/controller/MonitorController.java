package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.core.monitor.service.IMonitorService;
import com.raycloud.dmj.account.core.monitor.request.QueryShopMonitorReq;
import com.raycloud.dmj.account.core.monitor.vo.MonitorSummaryVO;
import com.raycloud.dmj.account.core.shop.req.ShopInfoRequest;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据监控
 */
@RestController
@RequestMapping("/${web.dynamic.path}/monitor")
public class MonitorController extends SessionController {

    @Resource
    private IMonitorService monitorService;

    /**
     * 数据监控数据列表
     *
     * @return
     */
    @PostMapping("/pageDataShopMonitor")
    public Response<List<MonitorSummaryVO>> pageDataShopMonitor(@RequestBody QueryShopMonitorReq req) {
        AccountUser accountUser = getAccountUser();
        req.setCompanyId(accountUser.getCompanyId());
        //查询资金账户
        return Response.success(monitorService.queryShopDataMonitor(req));
    }

    /**
     * 数据监控分页信息
     *
     * @return
     */
    @RequestMapping("/pageInfoShopMonitor")
    public Response<PageInfo<Void>> pageInfoShopMonitor(@RequestBody QueryShopMonitorReq req) {
        AccountUser accountUser = getAccountUser();
        req.setCompanyId(accountUser.getCompanyId());
        //查询资金账户
        return Response.success(monitorService.queryShopDataMonitorPageInfo(req));
    }

    /**
     * 数据监控数据列表
     *
     * @return
     */
    @PostMapping("tm/authData")
    public Response<?> tmAuthData(@RequestBody ShopInfoRequest req) {
        AccountUser accountUser = getAccountUser();
//        AccountUser accountUser = new AccountUser();
//        accountUser.setCompanyId(10438L);
        return Response.success(monitorService.queryTmAuthInfo(req, accountUser.getCompanyId()));
    }

    /**
     * 数据监控分页信息
     *
     * @return
     */
    @RequestMapping("tm/authDataPageInfo")
    public Response<?> tmAuthDataPageInfo(@RequestBody ShopInfoRequest req) {
        AccountUser accountUser = getAccountUser();
//        AccountUser accountUser = new AccountUser();
//        accountUser.setCompanyId(10438L);
        //查询资金账户
        return Response.success(monitorService.queryTmAuthInfoPage(req, accountUser.getCompanyId()));
    }

}
