package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.core.enums.PlatformEnum;
import com.raycloud.dmj.account.core.shop.req.*;
import com.raycloud.dmj.account.core.shop.service.IShopInfoService;
import com.raycloud.dmj.account.core.shop.vo.BatchAddResultVo;
import com.raycloud.dmj.account.core.shop.vo.ErpShopVO;
import com.raycloud.dmj.account.core.shop.vo.ShopInfoVO;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.session.SessionException;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 店铺信息管理
 */
@RequestMapping("/${web.dynamic.path}/shop")
@RestController
@Scope("prototype")
public class ShopController extends SessionController {

    @Resource
    private IShopInfoService shopInfoService;

    /**
     * 获取店铺信息列表
     * @param request
     * @return
     */
    @RequestMapping(value = "/getShopList")
    @ResponseBody
    public Response<List<ShopInfoVO>> getShopList(ShopInfoRequest request) {
        AccountUser accountUser = getAccountUser();
        List<ShopInfoVO> checkerVoList = shopInfoService.getShopList(accountUser,request);
        return Response.success(checkerVoList);
    }

    /**
     * 根据条件查询分页统计信息
     *
     * @param request 查询条件
     * @return 分页统计信息
     */
    @PostMapping ("/getPageInfo")
    public Response<PageInfo<Object>> getPageInfo(@RequestBody ShopInfoRequest request) {
        AccountUser accountUser = getAccountUser();
        PageInfo<Object> pageInfo = shopInfoService.getPageInfo(accountUser, request);
        return Response.success(pageInfo);
    }

    /**
     * 新增店铺信息
     */
    @PostMapping(value = "/addShopInfo")
    @ResponseBody
    public Response<Map<String,Object>> addShopInfo(@RequestBody AddShopInfoRequest addShopInfoRequest) {
        AccountUser accountUser = getAccountUser();
        Long id = shopInfoService.addShopInfo(accountUser, addShopInfoRequest);
        Map<String,Object> map = new HashMap<>();
        map.put("id",id);
        return Response.success(map);
    }

    /**
     * 批量新增店铺信息
     */
    @PostMapping(value = "/batchAddShopInfo")
    @ResponseBody
    public Response<BatchAddResultVo> batchAddShopInfo(@RequestBody BatchAddShopInfoRequest addShopInfoRequest) {
        AccountUser accountUser = getAccountUser();
        BatchAddResultVo resultVo = shopInfoService.batchAddShopInfo(accountUser, addShopInfoRequest);
        return Response.success(resultVo);
    }


    /**
     * 修改店铺期初余额
     */
    @PostMapping(value = "/updateShopAmount")
    @ResponseBody
    public Response<Map<String,Object>> updateShopAmount(@RequestBody ShopUpdateAmountRequest request) {
        AccountUser accountUser = getAccountUser();
        Long id = shopInfoService.updateShopAmount(accountUser,request);
        Map<String,Object> map = new HashMap<>();
        map.put("id",id);
        return Response.success(map);
    }

    /**
     * 批量修改店铺信息
     */
    @PostMapping(value = "/batchUpdateShopInfo")
    public Response<Void> batchUpdateShopInfo(@RequestBody ShopInfoUpdateInfoRequest request) {
        AccountUser accountUser = getAccountUser();
        shopInfoService.batchUpdateShopInfo(accountUser,request);
        return Response.success();
    }

    /**
     * 批量修改店铺对帐状态
     */
    @PostMapping(value = "/updateShopAmountState")
    @ResponseBody
    public Response<Object> updateShopAmountState(@RequestBody ShopInfoUpdateStateRequest request) {
        AccountUser accountUser = getAccountUser();
        shopInfoService.updateShopAmountState(accountUser,request);
        return Response.success();
    }

    /**
     * 批量修改店铺归属公司
     */
    @PostMapping(value = "/updateShopCompany")
    @ResponseBody
    public Response<Object> updateShopCompany(@RequestBody ShopInfoUpdateAffiliatedCompanyRequest request) {
        AccountUser accountUser = getAccountUser();
        shopInfoService.updateShopCompany(accountUser,request);
        return Response.success();
    }

    /**
     * 获取ERP店铺列表
     */
    @RequestMapping(value = "/getERPShopList")
    @ResponseBody
    public Response<Object> getERPShopList() throws SessionException {
        Staff staff = getStaff();
        List<ErpShopVO> shopList = shopInfoService.getERPShopList(staff);
        return Response.success(shopList);
    }

    /**
     *  获取平台信息
     */
    @RequestMapping(value = "/getPlatformCodeList")
    @ResponseBody
    public Response<Object> getPlatformCodeList(){
        List<Map<String,Object>> platformCodeList = PlatformEnum.getPlatformCodeList();
        return Response.success(platformCodeList);
    }

    /**
     *  获取简单店铺信息列表
     */
    @RequestMapping(value = "/getSimpleShopList")
    @ResponseBody
    public Response<List<ErpShopVO>> getSimpleShopList(String platformCodes){
        AccountUser accountUser = getAccountUser();
        List<ErpShopVO> shopList = shopInfoService.getSimpleShopList(accountUser,platformCodes);
        return Response.success(shopList);
    }


}
