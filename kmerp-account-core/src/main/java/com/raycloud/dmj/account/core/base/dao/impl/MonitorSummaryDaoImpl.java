package com.raycloud.dmj.account.core.base.dao.impl;

import com.alibaba.fastjson2.JSON;
import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.base.dao.MonitorSummaryDao;
import com.raycloud.dmj.account.core.base.domain.MonitorSummaryDO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.enums.field.MonitorSummaryFieldEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
@Slf4j
public class MonitorSummaryDaoImpl extends BaseDao implements MonitorSummaryDao {

    private static final String TABLE_NAME = "monitor_summary";

    @Override
    public Long insert(MonitorSummaryDO monitorSummaryDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(MonitorSummaryFieldEnum.getInsertFields())
                .valueForEntity(monitorSummaryDO)
                .columnNameCamelToUnderline()
                .toSql();

        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();

        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );

        // 获取生成的主键值
        return Objects.requireNonNull(keyHolder.getKey()).longValue();
    }

    @Override
    public void updateAnalyzeTimeByShopAndSource(Long companyId,Long shopId, Integer source,Date analyzeSuccessDate) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(source, "数据源不能为空！");
        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(MonitorSummaryFieldEnum.SHOP_ID.getFieldCode(), LinkMode.EQUAL, shopId),
                        Conditions.and(MonitorSummaryFieldEnum.DATA_SOURCE.getFieldCode(), LinkMode.EQUAL,source),
                        Conditions.and(MonitorSummaryFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId)
                )
                .update(
                        ColumnValues.create(MonitorSummaryFieldEnum.ANALYZE_SUCCESS_TIME.getFieldCode(), analyzeSuccessDate)
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int row = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (row <= 0){
            log.error("|MonitorSummaryDaoImpl.updateAnalyzeTimeByShopAndSource|更新数据库失败！sql={},updateArgs={}",sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR.getCode(),"数据不存在！");
        }
    }
    @Override
    public void insertOnDuplicateKey(MonitorSummaryDO monitorSummaryDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(MonitorSummaryFieldEnum.getInsertFields())
                .valueForEntity(monitorSummaryDO)
                .onDuplicateKey()
                .update(
                        ColumnValues.create(MonitorSummaryFieldEnum.ANALYZE_SUCCESS_TIME.getFieldCode(),monitorSummaryDO.getAnalyzeSuccessTime()),
                        ColumnValues.create(MonitorSummaryFieldEnum.IMPORT_SUCCESS_TIME.getFieldCode(),monitorSummaryDO.getImportSuccessTime()),
                        ColumnValues.create(MonitorSummaryFieldEnum.MODIFIED.getFieldCode(),new Date())
                )
                .columnNameCamelToUnderline()
                .toSql();
        jdbcTemplate.update(
                sql.getSqlCode(),
                sql.getArgs().toArray(new Object[0]));

    }




    @Override
    public List<MonitorSummaryDO> pageDataSummaryByPlatformCode(Long companyId, String platformCode, List<Long> shopIds, Page page) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(getSummaryByPlatformCondition(companyId, platformCode, shopIds))
                .select()
                .page(page.getPageNo(), page.getPageSize())
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        log.info("{}-sql:{}", Thread.currentThread().getName(), sql.toSqlString());
        List<MonitorSummaryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(MonitorSummaryDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public PageInfo<Void> pageInfoSummaryByPlatformCode(Long companyId, String platformCode, List<Long> shopIds, Page page) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(getSummaryByPlatformCondition(companyId, platformCode, shopIds))
                .select(Columns.create("count(*)"))
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        log.info("{}-sql:{}", Thread.currentThread().getName(), sql.toSqlString());
        Long total = jdbcTemplate.queryForObject(sql.getSqlCode(), Long.class, args);

        return PageInfo.<Void>builder()
                .total(total)
                .pageNo(page.getPageNo())
                .pageSize(page.getPageSize())
                .build();
    }

    private List<ConditionComponent<?>> getSummaryByPlatformCondition(Long companyId, String platformCode, List<Long> shopIds) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        conditions.add(Conditions.and(Columns.toColumn(MonitorSummaryFieldEnum.COMPANY_ID.getFieldCode()),
                LinkMode.EQUAL, companyId));
        conditions.add(Conditions.and(Columns.toColumn(MonitorSummaryFieldEnum.PLATFORM_CODE.getFieldCode()),
                LinkMode.EQUAL, platformCode));
        if (!CollectionUtils.isEmpty(shopIds)) {
            conditions.add(Conditions.and(Columns.toColumn(MonitorSummaryFieldEnum.SHOP_ID.getFieldCode()),
                    LinkMode.IN, shopIds));
        }
        return conditions;
    }

}
