package com.raycloud.dmj.account.core.base.dao.impl;

import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.base.dao.CategoryDao;
import com.raycloud.dmj.account.core.cleancategory.domain.QueryCategoryParam;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryDO;
import com.raycloud.dmj.account.core.enums.field.CategoryFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Repository
public class CategoryDaoImpl extends BaseDao implements CategoryDao {

    private final String TABLE_NAME = "category";

    @Override
    public Long insert(CategoryDO categoryDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(CategoryFieldEnum.getInsertFields())
                .valueForEntity(categoryDO)
                .columnNameCamelToUnderline()
                .toSql();

        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();
        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );
        // 获取生成的主键值
        return keyHolder.getKey().longValue();
    }

    @Override
    public CategoryDO getByPlatformAndFundIdAndCategoryAndName(Long companyId,Long fundAccountId,  String platformCode, String  name) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.PLATFORM_CODE.getFieldCode()), LinkMode.EQUAL, platformCode),
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.FUND_ACCOUNT_ID.getFieldCode()), LinkMode.EQUAL, fundAccountId),
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.NAME.getFieldCode()), LinkMode.EQUAL, name)
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public List<CategoryDO> listByFundId(Long companyId, Long fundAccountId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(fundAccountId, "资金账户ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.FUND_ACCOUNT_ID.getFieldCode()), LinkMode.EQUAL, fundAccountId)
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public List<CategoryDO> queryByIds(Long companyId, Set<Long> ids) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(ids, "ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.ID.getFieldCode()), LinkMode.IN, ids)

                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public List<CategoryDO> queryByParam(Long companyId, QueryCategoryParam param) {
        // 组装查询条件
        List<ConditionComponent<?>> conditions = getConditionComponents(companyId,param);
        // 构建关联查询
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        conditions
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public List<CategoryDO> queryByCompanyId(Long companyId) {
        // 组装查询条件
        // 构建关联查询
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(CategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    /**
     * 组装动态查询条件
     * @param companyId 公司ID
     * @param param 查询参数
     * @return 查询条件
     */
    private static List<ConditionComponent<?>> getConditionComponents(Long companyId,QueryCategoryParam param) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        //  组装查询条件
        conditions.add(Conditions.and(Columns.toColumn(CategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId));
        if (StringUtils.isNotEmpty(param.getPlatformCode())){
            conditions.add(Conditions.and(Columns.toColumn(CategoryFieldEnum.PLATFORM_CODE.getFieldCode()), LinkMode.EQUAL, param.getPlatformCode()));
        }
        //平台编码code集合
        if (CollectionUtils.isNotEmpty(param.getCategoryGroupCodes())){
            conditions.add(Conditions.and(Columns.toColumn(CategoryFieldEnum.CATEGORY_GROUP_CODE.getFieldCode()), LinkMode.IN, param.getCategoryGroupCodes()));
        }
        //分类code
        if (CollectionUtils.isNotEmpty(param.getCategoryGroupCodes())){
            conditions.add(Conditions.and(Columns.toColumn(CategoryFieldEnum.CATEGORY_GROUP_CODE.getFieldCode()), LinkMode.IN, param.getCategoryGroupCodes()));
        }
        //资金账户ID
        if (CollectionUtils.isNotEmpty(param.getFundAccountIds())){
            conditions.add(Conditions.and(Columns.toColumn(CategoryFieldEnum.FUND_ACCOUNT_ID.getFieldCode()), LinkMode.IN, param.getFundAccountIds()));
        }
        //分类ID
        if (CollectionUtils.isNotEmpty(param.getIdList())){
            conditions.add(Conditions.and(Columns.toColumn(CategoryFieldEnum.ID.getFieldCode()), LinkMode.IN, param.getIdList()));
        }
        return conditions;
    }


}
