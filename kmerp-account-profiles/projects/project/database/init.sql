
CREATE TABLE monitor_rule (
                              id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
                              created DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              `monitor_rule` JSON NOT NULL COMMENT '监控规则',
                              `company_id` BIGINT UNSIGNED NOT NULL COMMENT '公司ID',
                              `shop_id` BIGINT NOT NULL  COMMENT '店铺ID',
                              `data_source`  VARCHAR(32) NOT NULL COMMENT '数据类型：支付宝、微信、集分宝等',
                              `date_type` VARCHAR(128) NOT NULL COMMENT '账单类型：1=日，2=月，3=年',
                              INDEX `idx_shop_id` (`shop_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控规则表';

CREATE TABLE `monitor_summary` (
                                   `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
                                   `shop_id` BIGINT NOT NULL COMMENT '店铺ID',
                                   `company_id` BIGINT UNSIGNED NOT NULL COMMENT '公司ID',
                                   `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                   `platform_code` VARCHAR(32) NOT NULL COMMENT '平台code',
                                   `data_source` VARCHAR(32) NOT NULL COMMENT '数据类型：支付宝、微信、集分宝、淘金币',
                                   `import_success_time` DATETIME DEFAULT NULL COMMENT '导入成功的时间',
                                   `analyze_success_time` DATETIME DEFAULT NULL COMMENT '解析成功的时间',
                                   INDEX `idx_shop_id` (`shop_id`),
                                   INDEX `idx_company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控汇总表';

CREATE TABLE `shop_auth_record` (
                                    `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
                                    `shop_id` BIGINT NOT NULL COMMENT '店铺ID',
                                    `company_id` BIGINT UNSIGNED NOT NULL COMMENT '公司ID',
                                    `platform_code` varchar(32)  NOT NULL DEFAULT '' COMMENT '授权平台id',
                                    `call_back_url` VARCHAR(255) NOT NULL default '' COMMENT '回调url',
                                    `auth_status` VARCHAR(32)  DEFAULT 'NO_AUTHORIZE' COMMENT '授权状态',
                                    `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家授权记录表';

CREATE TABLE `shared_data_info` (
                                    `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
                                    `shop_id` BIGINT NOT NULL COMMENT '店铺ID',
                                    `company_id` BIGINT UNSIGNED NOT NULL COMMENT '公司ID',
                                    `platform_code` varchar(32) DEFAULT NULL COMMENT '授权平台id',
                                    `type` varchar(32) DEFAULT 'BILL' COMMENT '文件类型',
                                    `url` text NOT NULL COMMENT '文件路径',
                                    `date` DATE DEFAULT NULL COMMENT '账单的所属时间',
                                    `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                    INDEX `idx_shop_id` (`shop_id`),
                                    INDEX `idx_company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家账单数据表';

CREATE TABLE `shop_auth_info` (
                                  `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
                                  `shop_id` BIGINT NOT NULL COMMENT '店铺ID',
                                  `company_id` BIGINT NOT NULL COMMENT '公司ID',
                                  `platform_code` varchar(32) DEFAULT NULL COMMENT '授权平台id',
                                  `auth_status` VARCHAR(32) NOT NULL DEFAULT 'DISABLE' COMMENT '授权状态',
                                  `token` VARCHAR(255) NOT NULL COMMENT '授权凭证',
                                  `extra_data` text NULL  COMMENT '额外的授权信息',
                                  `expiration_time` DATETIME DEFAULT NULL COMMENT '授权过期时间',
                                  `created` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `modified` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                  INDEX `idx_shop_id` (`shop_id`),
                                  INDEX `idx_company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家授权信息表';
