spring:
  main:
    # webflux 启动必填，尤其是同时存在web和webflux依赖
    web-application-type: servlet
  application:
    name: kmerp-account-admin-boot
  profiles:
    active: @boot-env@
  # spring batch的配置，默认按照这个配置，不要动
  batch:
    job:
      enabled: false
    jdbc:
      initialize-schema: always
      platform: mysql
      schema: classpath:org/springframework/batch/core/schema-mysql.sql

# MyBatis配置
mybatis:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.raycloud.dmj.account.core.domain
  configuration:
    map-underscore-to-camel-case: true
    use-generated-keys: true
    default-executor-type: reuse
    default-statement-timeout: 30

# 配置这个是为了解决发布系统启动时的校验，如果不关闭jsp,请求project_auto_check_monitor.jsp会404
server:
  servlet:
    jsp:
      registered: false

dubbo:
  protocols:
    dubbo-protocol:
      name: dubbo
      port: 20880
    tri-protocol:
      name: tri
      port: 50051
  protocol:
    serialization: hessian2
    prefer-serialization: hessian2,fastjson2
  consumer:
    timeout: 10000
    retries: 3
    check: false
  application:
    name: @boot-name@
    logger: slf4j
  registries:
    erpZk:
      address: zookeeper://erp-zook.superboss.cc:30002
      register-mode: all
      default: true
    commonZk:
      address: zookeeper://zoo1.superboss.cc:30002,zookeeper://zoo2.superboss.cc:30002,zookeeper://zoo3.superboss.cc:30002
      register-mode: all
      default: false
  version:
    kmbi:
      core: 1.0.0


session:
  app:
    key: 1999
  diamond:
    config: ocs.m-k2j72fdb74e92294

cache:
  app:
    key: 1999
  diamond:
    config:
      new: ocs.m-k2j72fdb74e92294

kmerp:
  mq:
    rocket:
      producer:
        enable: true
        name-server: @mq-name-server@
        producer-group-name: kmerp-account-task-producer-group-@boot-env@
        unit-name: kmerp-account-task-producer-@boot-env@
      consumer:
        enable: true
        name-server: @mq-name-server@
        consumer-group-name: kmerp-account-task-producer-group-@boot-env@
        unit-name: kmerp-account-task-producer-@boot-env@
  dubbo:
    version:
      base: erp-@boot-env@-0.0.1
      trade: erp-trade-@boot-env@-0.0.1
      item: erp-item-@boot-env@-0.0.1
      item-search: erp-item-search-@boot-env@-0.0.1
      pt: erp-pt-@boot-env@-0.0.1
      pda: erp-pda-@boot-env@-0.0.1
      purchase: erp-caigou-@boot-env@-0.0.1
      aftersale: erp-aftersale-@boot-env@-0.0.1
      dms: erp-dms-@boot-env@-0.0.1
      fms: erp-fms-@boot-env@-0.0.1
      wms: erp-wms-@boot-env@-0.0.1
      logistics: erp-logistics-warning-@boot-env@-0.0.1
      chessboard: 1.0.0
  env: @boot-env@

application:
  env: @boot-env@
  serviceName: @boot-name@

web:
  dynamic:
    path: erpaccount