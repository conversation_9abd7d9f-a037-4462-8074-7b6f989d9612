spring:
  main:
    # webflux 启动必填，尤其是同时存在web和webflux依赖
    web-application-type: servlet
  application:
    name: kmerp-account-export-boot
  profiles:
    active: @boot-env@
  # spring batch的配置，默认按照这个配置，不要动
  batch:
    job:
      enabled: false
    jdbc:
      initialize-schema: always
      platform: mysql
      schema: classpath:org/springframework/batch/core/schema-mysql.sql
# 配置这个是为了解决发布系统启动时的校验，如果不关闭jsp,请求project_auto_check_monitor.jsp会404
server:
  servlet:
    jsp:
      registered: false

dubbo:
  protocols:
    dubbo-protocol:
      name: dubbo
      port: 20880
    tri-protocol:
      name: tri
      port: 50051
  protocol:
    serialization: hessian2
    prefer-serialization: hessian2,fastjson2
  application:
    name: kmerp-account-export-boot
    logger: slf4j
  registries:
    erpZk:
      address: zookeeper://erp-zook.superboss.cc:30002
      register-mode: all
      default: true
    commonZk:
      address: zookeeper://zoo1.superboss.cc:30002zookeeper://zoo2.superboss.cc:30002,zookeeper://zoo3.superboss.cc:30002
      register-mode: all
      default: false
  version:
    kmbi:
      core: 1.0.0

